import { useCallback, useEffect, useMemo, useState } from 'react';
import type { TimetableEntry, DayOfWeek } from '../types/timetable';
import { emploiTemps } from '../services/emploiTemps';
import { days } from '../utils/day';
import { compareHHmm } from '../utils/time';

type Grouped = Record<DayOfWeek, TimetableEntry[]>;

function emptyGrouped(): Grouped {
  return days.reduce((acc, d) => {
    acc[d] = [];
    return acc;
  }, {} as Grouped);
}

export function useTimetables(classId: string): {
  data: TimetableEntry[] | undefined;
  groupedByDay: Record<DayOfWeek, TimetableEntry[]>;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
} {
  const [data, setData] = useState<TimetableEntry[]>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!classId) {
      setData([]);
      setError(new Error('classId requis'));
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const list = await emploiTemps.listByClass(classId);
      // Ensure sorted by startTime for each subsequent grouping
      const sorted = [...list].sort((a, b) => compareHHmm(a.startTime, b.startTime));
      setData(sorted);
    } catch (e: any) {
      const msg = e?.message || 'Erreur lors du chargement de l’emploi du temps';
      setError(new Error(msg));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, [classId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const groupedByDay = useMemo(() => {
    const base = emptyGrouped();
    if (!data || data.length === 0) return base;

    for (const entry of data) {
      const day = entry.day;
      if (!base[day]) base[day] = [];
      base[day].push(entry);
    }

    // sort each day by startTime
    for (const d of days) {
      base[d] = (base[d] || []).slice().sort((a, b) => compareHHmm(a.startTime, b.startTime));
    }

    return base;
  }, [data]);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  return { data, groupedByDay, isLoading, error, refetch };
}