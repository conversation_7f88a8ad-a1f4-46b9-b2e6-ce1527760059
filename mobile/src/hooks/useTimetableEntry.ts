import { useCallback, useRef, useState } from 'react';
import type {
  TimetableEntry,
  CreateTimetableEntryInput,
  UpdateTimetableEntryInput,
} from '../types/timetable';
import { emploiTemps } from '../services/emploiTemps';

/**
 * useTimetableEntry
 * - Provides CRUD helpers for a single timetable entry.
 * - After successful mutations, increments a local version token to allow optional
 *   consumers to read and react (lightweight invalidation without global cache).
 * - Screens can call useTimetables(...).refetch() as needed.
 */

export function useTimetableEntry(classId?: string): {
  getEntry: (id: string) => Promise<TimetableEntry>;
  createEntry: (input: CreateTimetableEntryInput) => Promise<TimetableEntry>;
  updateEntry: (id: string, patch: UpdateTimetableEntryInput) => Promise<TimetableEntry>;
  deleteEntry: (id: string) => Promise<void>;
  isMutating: boolean;
  error: Error | null;
} {
  const [isMutating, setIsMutating] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  // simple local invalidation counter (not exported globally)
  const versionRef = useRef(0);

  const withMutation = useCallback(async <T,>(fn: () => Promise<T>): Promise<T> => {
    setIsMutating(true);
    setError(null);
    try {
      const res = await fn();
      // lightweight invalidation
      versionRef.current += 1;
      return res;
    } catch (e: any) {
      const msg = e?.message || 'Erreur lors de l’opération';
      const err = new Error(msg);
      setError(err);
      throw err;
    } finally {
      setIsMutating(false);
    }
  }, []);

  const getEntry = useCallback(async (id: string): Promise<TimetableEntry> => {
    // getById will fallback to listByClass if backend route is missing
    return emploiTemps.getById(id, classId);
  }, [classId]);

  const createEntry = useCallback(async (input: CreateTimetableEntryInput): Promise<TimetableEntry> => {
    return withMutation(async () => {
      const created = await emploiTemps.create(input);
      return created;
    });
  }, [withMutation]);

  const updateEntry = useCallback(async (id: string, patch: UpdateTimetableEntryInput): Promise<TimetableEntry> => {
    return withMutation(async () => {
      const updated = await emploiTemps.update(id, patch);
      return updated;
    });
  }, [withMutation]);

  const deleteEntry = useCallback(async (id: string): Promise<void> => {
    await withMutation(async () => {
      await emploiTemps.remove(id);
      return undefined as unknown as void;
    });
  }, [withMutation]);

  return { getEntry, createEntry, updateEntry, deleteEntry, isMutating, error };
}