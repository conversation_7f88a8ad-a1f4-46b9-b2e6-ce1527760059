import { getHttpClient } from './httpClient';
import { getApiUrl } from '../config/api';
import type {
  TimetableEntry,
  CreateTimetableEntryInput,
  UpdateTimetableEntryInput,
  DayOfWeek,
} from '../types/timetable';
import { normalizeDay } from '../utils/day';
import { clampToHHmm, isHHmm } from '../utils/time';

/**
 * Emploi du temps Service
 * - Probes base path once: tries "/api/emploi_temps", falls back to "/api/emploi-temps" on 404.
 * - Uses shared http client/token handling consistent with other services.
 * - Adapts DTOs between backend snake_case/French and frontend camelCase/English.
 */

type AnyRecord = Record<string, any>;

let EMPLOI_BASE_PATH: string | null = null;
let probingPromise: Promise<string> | null = null;

async function probeBasePath(): Promise<string> {
  // Backend confirmed base path
  EMPLOI_BASE_PATH = '/api/emploi_temps';
  return EMPLOI_BASE_PATH;
}
/**
 * Defensive string id coercion
 */
function asId(v: unknown): string {
  if (v == null) return '';
  return String(v);
}

/**
 * Normalize time fields to HH:mm
 */
function toHHmm(v: any): string {
  const s = v == null ? '' : String(v).trim();
  if (!s) return '';
  if (isHHmm(s)) return s;
  // Try to coerce common cases like "8:30" => "08:30"
  const parts = s.split(':');
  if (parts.length === 2) {
    const h = parts[0].padStart(2, '0');
    const m = parts[1].padStart(2, '0');
    const candidate = `${h}:${m}`;
    if (isHHmm(candidate)) return candidate;
  }
  // If still invalid, let clamp throw to surface a clear error
  return clampToHHmm(s);
}

/**
 * fromDTO maps backend variations to TimetableEntry (frontend)
 * Accepts both French snake_case and English camelCase.
 */
function fromDTO(dto: AnyRecord): TimetableEntry {
  const dayRaw =
    dto.jour_semaine ??
    dto.jour ??
    dto.day ??
    dto.dayOfWeek ??
    dto.weekday;

  const startRaw = dto.heure_debut ?? dto.startTime ?? dto.start ?? dto.heureDebut;
  const endRaw = dto.heure_fin ?? dto.endTime ?? dto.end ?? dto.heureFin;

  const entry: TimetableEntry = {
    id: asId(dto.emploi_id ?? dto.id ?? dto._id),
    classId: asId(dto.classe_id ?? dto.classId ?? dto.class_id),
    day: normalizeDay(dayRaw),
    startTime: toHHmm(startRaw || ''),
    endTime: toHHmm(endRaw || ''),
    courseId: asId(dto.cours_id ?? dto.courseId ?? dto.course_id ?? dto.matiere_id),
    teacherId: asId(dto.prof_id ?? dto.teacherId ?? dto.professeur_id),
    roomId: asId(dto.salle_id ?? dto.roomId),
    notes: dto.notes ?? dto.remarque ?? dto.commentaire ?? undefined,
    createdAt: dto.created_at ?? dto.createdAt ?? undefined,
    updatedAt: dto.updated_at ?? dto.updatedAt ?? undefined,
    courseName: dto.courseName ?? dto.nom_matiere ?? dto.matiere ?? undefined,
    teacherName: dto.teacherName ?? dto.nom_prof ?? dto.professeur ?? undefined,
    roomName: dto.roomName ?? dto.nom_salle ?? dto.salle ?? undefined,
  };

  return entry;
}

/**
 * toCreatePayload maps CreateTimetableEntryInput to backend payload
 */
function toCreatePayload(input: CreateTimetableEntryInput): AnyRecord {
  // Backend expects French snake_case with class name, subject id, etc.
  return {
    nom_classe: input.classId, // classId holds class name in this app
    jour_semaine: normalizeDay(input.day),
    heure_debut: toHHmm(input.startTime),
    heure_fin: toHHmm(input.endTime),
    // Prefer matiere_id; keep legacy keys only if backend requires
    matiere_id: input.courseId,
    salle_id: input.roomId,
    notes: input.notes,
  };
}

/**
 * toUpdatePayload maps UpdateTimetableEntryInput partial to backend payload
 */
function toUpdatePayload(patch: UpdateTimetableEntryInput): AnyRecord {
  const out: AnyRecord = {};
  if (patch.classId != null) out.classe_id = patch.classId;
  if (patch.day != null) {
    const d = normalizeDay(patch.day as string);
    out.jour = d;
    out.day = d;
  }
  if (patch.startTime != null) out.heure_debut = toHHmm(patch.startTime);
  if (patch.endTime != null) out.heure_fin = toHHmm(patch.endTime);
  if (patch.courseId != null) out.cours_id = patch.courseId;
  if (patch.teacherId != null) out.prof_id = patch.teacherId;
  if (patch.roomId != null) out.salle_id = patch.roomId;
  if (patch.notes !== undefined) out.notes = patch.notes;
  return out;
}

/**
 * Resolve entry by listing a class planning and finding locally.
 * Backend does not expose GET /api/emploi_temps/:id.
 */
async function resolveByClassList(id: string, classId: string): Promise<TimetableEntry> {
  const list = await emploiTemps.listByClass(classId);
  const found = list.find(e => e.id === id);
  if (!found) {
    throw new Error(`Timetable entry ${id} not found within classId=${classId}`);
  }
  return found;
}

export const emploiTemps = {
  /**
   * List timetable entries by class
   * GET {base}/planning/:nom_classe
   */
  async listByClass(classId: string): Promise<TimetableEntry[]> {
    const base = await probeBasePath();
    const http = getHttpClient();

    const url = `${base}/planning/${encodeURIComponent(classId)}`;
    try {
      const res = await http.get<any>(url);
      const data = res?.data ?? res;

      if (Array.isArray(data)) {
        return data.map(fromDTO);
      }
      if (Array.isArray(data?.creneaux)) {
        return data.creneaux.map(fromDTO);
      }
      if (Array.isArray(data?.data?.creneaux)) {
        return data.data.creneaux.map(fromDTO);
      }
      if (Array.isArray(data?.data)) {
        return data.data.map(fromDTO);
      }

      const arr = data?.emploiTemps ?? data?.items ?? [];
      return (Array.isArray(arr) ? arr : []).map(fromDTO);
    } catch (e: any) {
      const msg = e?.response?.message || e?.message || 'Erreur lors du chargement de l’emploi du temps';
      throw new Error(msg);
    }
  },
  /**
   * Get a timetable entry by id.
   * Backend does NOT provide GET /api/emploi_temps/:id.
   * Always requires classId to resolve via listByClass.
   */
  async getById(id: string, classIdIfNeeded?: string): Promise<TimetableEntry> {
    if (!classIdIfNeeded) {
      throw new Error('Backend does not expose GET /api/emploi_temps/:id; provide classId to resolve via listByClass.');
    }
    return await resolveByClassList(id, classIdIfNeeded);
  },

  /**
   * Create a timetable entry
   */
  async create(input: CreateTimetableEntryInput): Promise<TimetableEntry> {
    const base = await probeBasePath();
    const http = getHttpClient();
    const payload = toCreatePayload(input);
    try {
      const res = await http.post<any>(base, payload);
      const data = res?.data ?? res;
      return fromDTO(data);
    } catch (e: any) {
      const msg = e?.response?.message || e?.message || 'Erreur lors de la création';
      throw new Error(msg);
    }
  },

  /**
   * Update a timetable entry
   */
  async update(id: string, patch: UpdateTimetableEntryInput): Promise<TimetableEntry> {
    const base = await probeBasePath();
    const http = getHttpClient();
    const payload = toUpdatePayload(patch);
    try {
      const res = await http.put<any>(`${base}/${encodeURIComponent(id)}`, payload);
      const data = res?.data ?? res;
      return fromDTO(data);
    } catch (e: any) {
      const msg = e?.response?.message || e?.message || 'Erreur lors de la mise à jour';
      throw new Error(msg);
    }
  },

  /**
   * Remove an entry
   */
  async remove(id: string): Promise<{ success: boolean }> {
    const base = await probeBasePath();
    const http = getHttpClient();
    try {
      await http.delete(`${base}/${encodeURIComponent(id)}`);
      return { success: true };
    } catch (e: any) {
      const msg = e?.response?.message || e?.message || 'Erreur lors de la suppression';
      throw new Error(msg);
    }
  },
};

export default emploiTemps;