const HHMM_REGEX = /^([01]\d|2[0-3]):[0-5]\d$/;

export function isHHmm(value: string): boolean {
  return HHMM_REGEX.test(value);
}

function parseHHmm(value: string): { h: number; m: number } {
  if (!isHHmm(value)) {
    throw new Error(`Invalid time format, expected HH:mm, received "${value}"`);
  }
  const [h, m] = value.split(':');
  return { h: Number(h), m: Number(m) };
}

export function compareHHmm(a: string, b: string): number {
  const pa = parseHHmm(a);
  const pb = parseHHmm(b);
  const da = pa.h * 60 + pa.m;
  const db = pb.h * 60 + pb.m;
  if (da < db) return -1;
  if (da > db) return 1;
  return 0;
}

export function clampToHHmm(value: string): string {
  const trimmed = value.trim();
  if (!isHHmm(trimmed)) {
    throw new Error(`Time is not in HH:mm format: "${value}"`);
  }
  return trimmed;
}

export function ensureStartBeforeEnd(start: string, end: string): boolean {
  return compareHHmm(start, end) < 0;
}