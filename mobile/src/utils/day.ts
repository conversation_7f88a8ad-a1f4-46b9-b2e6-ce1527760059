import type { DayOfWeek } from '../types/timetable';

export const days: DayOfWeek[] = ['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];

const frToEn: Record<string, DayOfWeek> = {
  lundi: 'monday',
  mardi: 'tuesday',
  mercredi: 'wednesday',
  jeudi: 'thursday',
  vendredi: 'friday',
  samedi: 'saturday',
  dimanche: 'sunday',
};

const enDaysSet = new Set(days);

export function normalizeDay(input: string | number): DayOfWeek {
  if (typeof input === 'number') {
    const n = Math.trunc(input);
    if (n < 1 || n > 7) throw new Error('Day number must be between 1 and 7');
    return days[n - 1];
  }

  const raw = String(input).trim().toLowerCase();

  // numeric-like strings
  const asNum = Number(raw);
  if (!Number.isNaN(asNum) && Number.isFinite(asNum)) {
    return normalizeDay(asNum);
  }

  // english direct
  if (enDaysSet.has(raw as DayOfWeek)) {
    return raw as DayOfWeek;
  }

  // french mapping
  const mapped = frToEn[raw];
  if (mapped) return mapped;

  throw new Error(`Unrecognized day: ${input}`);
}

export function toFrenchDay(day: DayOfWeek): string {
  switch (day) {
    case 'monday': return 'lundi';
    case 'tuesday': return 'mardi';
    case 'wednesday': return 'mercredi';
    case 'thursday': return 'jeudi';
    case 'friday': return 'vendredi';
    case 'saturday': return 'samedi';
    case 'sunday': return 'dimanche';
    default: {
      // Exhaustiveness guard
      const _exhaustive: never = day;
      return _exhaustive;
    }
  }
}

export function toNumericDay(day: DayOfWeek): number {
  const idx = days.indexOf(day);
  if (idx === -1) throw new Error(`Invalid DayOfWeek: ${String(day)}`);
  return idx + 1;
}