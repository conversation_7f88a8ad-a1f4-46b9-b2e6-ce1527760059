export type DayOfWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

export type TimeString = string;

export type TimetableEntry = {
  id: string;
  classId: string;
  day: DayOfWeek;
  startTime: TimeString;
  endTime: TimeString;
  courseId: string;
  teacherId: string;
  roomId: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
  courseName?: string;
  teacherName?: string;
  roomName?: string;
};

export type CreateTimetableEntryInput = {
  classId: string;
  day: DayOfWeek;
  startTime: TimeString;
  endTime: TimeString;
  courseId: string;
  teacherId: string;
  roomId: string;
  notes?: string;
};

export type UpdateTimetableEntryInput = Partial<CreateTimetableEntryInput>;