/**
 * SUMMARY: Replaced raw ID text inputs with Select-based dropdowns backed by existing services.
 * - Added loading of classes (nom_classe), cours, professeurs, salles and mapped to Select options.
 * - Kept internal state as selected values (strings). Classe uses nom_classe as value.
 * - Validation: required fields, HH:mm format, end after start.
 * - Submit disabled logic moved to parent screens; inline errors preserved.
 * - No backend changes; payload mapping unchanged (create.nom_classe <= classId, update uses *_id fields).
 */
import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, useColorScheme, ActivityIndicator } from 'react-native';
import Select from '../../../components/Select';
import {
  TimetableEntry,
  CreateTimetableEntryInput,
  UpdateTimetableEntryInput,
} from '../../../types/timetable';
import { days, normalizeDay } from '../../../utils/day';
import { ensureStartBeforeEnd, isHHmm } from '../../../utils/time';
import { classesService } from '../../../services/classes';
import coursesService from '../../../services/courses';
import { ApiServices } from '../../../services/apiService';
import { sallesService } from '../../../services/sallesService';

type Props = {
  initial?: Partial<TimetableEntry>;
  classIdLocked?: boolean;
  onSubmit: (input: CreateTimetableEntryInput | UpdateTimetableEntryInput) => void | Promise<void>;
  onCancel?: () => void;
  submitLabel?: string;
};

type Errors = Partial<Record<keyof CreateTimetableEntryInput | 'form', string>>;

type Option = { label: string; value: string };

const TimetableForm: React.FC<Props> = ({
  initial,
  classIdLocked,
  onSubmit,
  onCancel,
  submitLabel = 'Save',
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Prefill
  const initialDay = useMemo(() => normalizeDay(String(initial?.day ?? '')), [initial?.day]);

  const [classId, setClassId] = useState<string>(String(initial?.classId ?? ''));
  const [day, setDay] = useState<string>(initialDay || '');
  const [startTime, setStartTime] = useState<string>(String(initial?.startTime ?? ''));
  const [endTime, setEndTime] = useState<string>(String(initial?.endTime ?? ''));
  const [courseId, setCourseId] = useState<string>(String((initial as any)?.courseId ?? ''));
  const [teacherId, setTeacherId] = useState<string>(String((initial as any)?.teacherId ?? ''));
  const [roomId, setRoomId] = useState<string>(String((initial as any)?.roomId ?? ''));
  const [notes, setNotes] = useState<string>(String(initial?.notes ?? ''));
  const [errors, setErrors] = useState<Errors>({});

  // Options state
  const [loadingOptions, setLoadingOptions] = useState<boolean>(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [classOptions, setClassOptions] = useState<Option[]>([]);
  const [courseOptions, setCourseOptions] = useState<Option[]>([]);
  const [teacherOptions, setTeacherOptions] = useState<Option[]>([]);
  const [roomOptions, setRoomOptions] = useState<Option[]>([]);

  useEffect(() => {
    let mounted = true;
    (async () => {
      setLoadingOptions(true);
      setLoadError(null);
      try {
        // Classes (nom_classe as value)
        const classes = await classesService.list();
        const classOpts: Option[] = (classes || []).map((c: any, idx: number) => {
          const label = c?.nom_classe || c?.nom || c?.name || `Classe ${idx + 1}`;
          const value = String(c?.nom_classe ?? c?.nom ?? c?.name ?? '');
          return { label, value };
        });

        // Courses (id + name)
        const cours = await coursesService.list();
        const courseOpts: Option[] = (cours || []).map((co: any) => {
          const id = String(co?.id ?? co?.matiere_id ?? co?.cours_id ?? '');
          const label = co?.nom ?? co?.name ?? co?.matiere ?? `Cours ${id}`;
          return { label, value: id };
        });

        // Teachers via ApiServices.user().getProfesseurs()
        const profs = await ApiServices.user().getProfesseurs();
        const teacherOpts: Option[] = (profs || []).map((p: any) => {
          const matricule = String(p?.matricule ?? p?.id ?? '');
          const fullName = [p?.nom, p?.prenom].filter(Boolean).join(' ') || p?.name || `Prof ${matricule}`;
          return { label: `${fullName} (${matricule})`, value: matricule };
        });

        // Salles
        const salles = await sallesService.getSalles();
        const salleArray = Array.isArray(salles) ? salles : (salles as any)?.data ?? [];
        const roomOpts: Option[] = (salleArray || []).map((s: any) => {
          const id = String(s?.id ?? s?.salle_id ?? s?.code ?? '');
          const label = s?.nom ?? s?.name ?? s?.code ?? `Salle ${id}`;
          return { label, value: id };
        });

        if (!mounted) return;
        setClassOptions(classOpts);
        setCourseOptions(courseOpts);
        setTeacherOptions(teacherOpts);
        setRoomOptions(roomOpts);
      } catch (e: any) {
        if (!mounted) return;
        setLoadError(e?.message || 'Erreur lors du chargement des options');
      } finally {
        if (mounted) setLoadingOptions(false);
      }
    })();
    return () => {
      mounted = false;
    };
  }, []);

  const required = (v: string) => v.trim().length > 0;

  const validate = (): boolean => {
    const next: Errors = {};
    if (!classIdLocked && !required(classId)) next.classId = 'Class is required';
    if (!required(day)) next.day = 'Day is required';
    if (!required(startTime)) next.startTime = 'Start time is required';
    if (!required(endTime)) next.endTime = 'End time is required';
    if (required(startTime) && !isHHmm(startTime)) next.startTime = 'Use HH:mm format';
    if (required(endTime) && !isHHmm(endTime)) next.endTime = 'Use HH:mm format';
    if (required(startTime) && required(endTime) && isHHmm(startTime) && isHHmm(endTime)) {
      if (!ensureStartBeforeEnd(startTime, endTime)) {
        next.endTime = 'End must be after start';
      }
    }
    if (!required(courseId)) next.courseId = 'Course is required';
    if (!required(teacherId)) next.teacherId = 'Teacher is required';
    if (!required(roomId)) next.roomId = 'Room is required';

    setErrors(next);
    return Object.keys(next).length === 0;
  };

  const buildCreateInput = (): CreateTimetableEntryInput => {
    return {
      // classId carries nom_classe per constraint
      classId: classIdLocked ? String(initial?.classId ?? '') : classId.trim(),
      day: normalizeDay(day),
      startTime: startTime.trim(),
      endTime: endTime.trim(),
      courseId: courseId.trim(),
      teacherId: teacherId.trim(),
      roomId: roomId.trim(),
      notes: notes.trim() || undefined,
    };
  };

  const buildUpdateInput = (): UpdateTimetableEntryInput => {
    const normInitialDay = normalizeDay(String(initial?.day ?? ''));
    const currentNormalizedDay = normalizeDay(day);

    const diff: UpdateTimetableEntryInput = {};

    if (!classIdLocked) {
      const prev = String(initial?.classId ?? '');
      if (prev !== classId) diff.classId = classId.trim();
    }
    if (normInitialDay !== currentNormalizedDay) diff.day = currentNormalizedDay;

    const prevStart = String(initial?.startTime ?? '');
    const prevEnd = String(initial?.endTime ?? '');
    if (prevStart !== startTime) diff.startTime = startTime.trim();
    if (prevEnd !== endTime) diff.endTime = endTime.trim();

    const prevCourse = String((initial as any)?.courseId ?? '');
    const prevTeacher = String((initial as any)?.teacherId ?? '');
    const prevRoom = String((initial as any)?.roomId ?? '');
    const prevNotes = String(initial?.notes ?? '');

    if (prevCourse !== courseId) diff.courseId = courseId.trim();
    if (prevTeacher !== teacherId) diff.teacherId = teacherId.trim();
    if (prevRoom !== roomId) diff.roomId = roomId.trim();

    if (prevNotes !== notes) diff.notes = notes.trim() || undefined;

    return diff;
  };

  const handleSubmit = async () => {
    if (!validate()) return;

    const hasId = Boolean((initial as any)?.id);
    const payload = hasId ? buildUpdateInput() : buildCreateInput();

    try {
      await onSubmit(payload);
    } catch (e) {
      setErrors((prev) => ({ ...prev, form: 'Failed to submit. Please try again.' }));
    }
  };

  return (
    <View style={styles.container}>
      {/* Error banner */}
      {errors.form ? (
        <View style={[styles.alert, { borderColor: '#ef4444', backgroundColor: 'rgba(239,68,68,0.08)' }]}>
          <Text style={{ color: '#ef4444' }}>{errors.form}</Text>
        </View>
      ) : null}
      {loadError ? (
        <View style={[styles.alert, { borderColor: '#ef4444', backgroundColor: 'rgba(239,68,68,0.08)' }]}>
          <Text style={{ color: '#ef4444' }}>{loadError}</Text>
        </View>
      ) : null}

      {/* Classe (nom_classe) */}
      <View style={styles.field}>
        <Select
          label="Classe"
          placeholder="Sélectionner la classe"
          options={classOptions}
          value={classId}
          onValueChange={setClassId}
          style={{}}
          disabled={!!classIdLocked}
        />
        {!classIdLocked && errors.classId ? <Text style={styles.errorInline}>{errors.classId}</Text> : null}
      </View>

      {/* Day */}
      <View style={styles.field}>
        <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Jour</Text>
        <View style={styles.daysRow}>
          {days.map((d) => {
            const selected = day === d;
            return (
              <TouchableOpacity
                key={d}
                accessibilityRole="button"
                accessibilityLabel={`Select ${d}`}
                onPress={() => setDay(d)}
                style={[
                  styles.dayChip,
                  {
                    borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.12)',
                    backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
                  },
                  selected && (isDark ? styles.dayChipSelectedDark : styles.dayChipSelectedLight),
                ]}
              >
                <Text
                  style={[
                    styles.dayChipText,
                    {
                      color: selected
                        ? (isDark ? '#60A5FA' : '#1D4ED8') // primary-ish for selected
                        : (isDark ? '#E2E8F0' : '#111827'),
                      textTransform: 'capitalize',
                    },
                    selected && styles.dayChipTextSelected,
                  ]}
                >
                  {d}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
        {errors.day ? <Text style={styles.errorInline}>{errors.day}</Text> : null}
      </View>

      {/* Start / End time */}
      <View style={styles.row}>
        <View style={[styles.field, styles.half]}>
          <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Heure début</Text>
          <TextInput
            accessibilityLabel="Heure début"
            placeholder="HH:mm"
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            value={startTime}
            onChangeText={setStartTime}
            keyboardType="numbers-and-punctuation"
            style={[
              styles.input,
              {
                color: isDark ? '#F8FAFC' : '#0F172A',
                borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
                backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
              },
            ]}
          />
          {errors.startTime ? <Text style={styles.errorInline}>{errors.startTime}</Text> : null}
        </View>

        <View style={[styles.field, styles.half]}>
          <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Heure fin</Text>
          <TextInput
            accessibilityLabel="Heure fin"
            placeholder="HH:mm"
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            value={endTime}
            onChangeText={setEndTime}
            keyboardType="numbers-and-punctuation"
            style={[
              styles.input,
              {
                color: isDark ? '#F8FAFC' : '#0F172A',
                borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
                backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
              },
            ]}
          />
          {errors.endTime ? <Text style={styles.errorInline}>{errors.endTime}</Text> : null}
        </View>
      </View>

      {/* Course */}
      <View style={styles.field}>
        <Select
          label="Cours"
          placeholder="Sélectionner le cours"
          options={courseOptions}
          value={courseId}
          onValueChange={setCourseId}
          style={{}}
          disabled={loadingOptions}
        />
        {errors.courseId ? <Text style={styles.errorInline}>{errors.courseId}</Text> : null}
      </View>

      {/* Teacher */}
      <View style={styles.field}>
        <Select
          label="Professeur"
          placeholder="Sélectionner le professeur"
          options={teacherOptions}
          value={teacherId}
          onValueChange={setTeacherId}
          style={{}}
          disabled={loadingOptions}
        />
        {errors.teacherId ? <Text style={styles.errorInline}>{errors.teacherId}</Text> : null}
      </View>

      {/* Room */}
      <View style={styles.field}>
        <Select
          label="Salle"
          placeholder="Sélectionner la salle"
          options={roomOptions}
          value={roomId}
          onValueChange={setRoomId}
          style={{}}
          disabled={loadingOptions}
        />
        {errors.roomId ? <Text style={styles.errorInline}>{errors.roomId}</Text> : null}
      </View>

      {/* Notes */}
      <View style={styles.field}>
        <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Notes (optionnel)</Text>
        <TextInput
          accessibilityLabel="Notes"
          placeholder="Any notes..."
          placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
          value={notes}
          onChangeText={setNotes}
          style={[
            styles.input,
            styles.notesInput,
            {
              color: isDark ? '#F8FAFC' : '#0F172A',
              borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
              backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
            },
          ]}
          multiline
        />
      </View>

      {/* Actions */}
      <View style={styles.actionsRow}>
        {onCancel ? (
          <TouchableOpacity
            accessibilityRole="button"
            accessibilityLabel="Cancel"
            onPress={onCancel}
            style={[styles.smallBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>Annuler</Text>
          </TouchableOpacity>
        ) : null}

        <TouchableOpacity
          accessibilityRole="button"
          accessibilityLabel={submitLabel}
          onPress={handleSubmit}
          style={[styles.submitBtn, { backgroundColor: '#2563EB', opacity: loadingOptions ? 0.7 : 1 }]}
          disabled={loadingOptions}
        >
          {loadingOptions ? <ActivityIndicator color="#fff" /> : <Text style={{ color: '#fff', fontWeight: '800' }}>{submitLabel}</Text>}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { padding: 16 },
  field: { marginBottom: 12 },
  label: { fontWeight: '800', marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, padding: 10 },
  inputDisabled: { backgroundColor: '#F3F4F6', color: '#6B7280' },
  notesInput: { minHeight: 72, textAlignVertical: 'top' },
  errorInline: { marginTop: 4, color: '#B91C1C' },
  row: { flexDirection: 'row', gap: 12 },
  half: { flex: 1 },
  daysRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },
  dayChip: { borderWidth: 1, borderRadius: 16, paddingVertical: 6, paddingHorizontal: 10 },
  // Stronger selected contrast
  dayChipSelectedLight: { backgroundColor: 'rgba(37,99,235,0.16)', borderColor: '#3B82F6' },
  dayChipSelectedDark: { backgroundColor: 'rgba(37,99,235,0.22)', borderColor: '#60A5FA' },
  dayChipText: {},
  dayChipTextSelected: { fontWeight: '700' },
  actionsRow: { flexDirection: 'row', gap: 12, justifyContent: 'flex-end', marginTop: 8 },
  smallBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  submitBtn: { paddingVertical: 12, paddingHorizontal: 16, alignItems: 'center', borderRadius: 12 },
  alert: { padding: 10, borderRadius: 8, borderWidth: 1, marginBottom: 12 },
});

export default TimetableForm;