import React, { memo, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SectionList, useColorScheme } from 'react-native';
import { TimetableEntry } from '../../../types/timetable';
import { normalizeDay, days } from '../../../utils/day';
import { isHHmm } from '../../../utils/time';

type Props = {
  entries: TimetableEntry[];
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
};

type Section = {
  title: string;
  data: TimetableEntry[];
};

function sortByStartTime(a: TimetableEntry, b: TimetableEntry) {
  // naive lexical compare since times are HH:mm
  if (a.startTime < b.startTime) return -1;
  if (a.startTime > b.startTime) return 1;
  return 0;
}

const TimetableList: React.FC<Props> = ({ entries, onEdit, onDelete }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const sections = useMemo<Section[]>(() => {
    const byDay: Record<string, TimetableEntry[]> = {};
    for (const e of entries ?? []) {
      const day = normalizeDay((e as any).day ?? '');
      if (!day) continue;
      if (!byDay[day]) byDay[day] = [];
      byDay[day].push(e);
    }
    const result: Section[] = [];
    for (const d of days) {
      const list = byDay[d];
      if (list && list.length) {
        result.push({
          title: d,
          data: [...list].sort(sortByStartTime),
        });
      }
    }
    return result;
  }, [entries]);

  const renderItem = ({ item }: { item: TimetableEntry }) => {
    const start = isHHmm(item.startTime) ? item.startTime : String(item.startTime ?? '');
    const end = isHHmm(item.endTime) ? item.endTime : String(item.endTime ?? '');
    const timeRange = `${start} - ${end}`;
    const course = (item as any).courseName || (item as any).courseId || '-';
    const teacher = (item as any).teacherName || (item as any).teacherId || '-';
    const room = (item as any).roomName || (item as any).roomId || '-';

    return (
      <View
        style={[
          styles.item,
          {
            borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
            backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
          },
        ]}
        accessibilityLabel={`Horaire ${timeRange}`}
      >
        <View style={{ flex: 1, paddingRight: 8 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
            <Text style={[styles.itemTitle, { color: isDark ? '#F8FAFC' : '#0F172A', flex: 1 }]} numberOfLines={1}>
              {timeRange}
            </Text>
            <View
              style={[
                styles.chip,
                {
                  borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.12)',
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(15,23,42,0.03)',
                },
              ]}
            >
              <Text style={{ fontSize: 12, color: isDark ? '#E2E8F0' : '#0F172A' }} numberOfLines={1}>
                {String(course)}
              </Text>
            </View>
            <Text style={{ color: isDark ? '#94A3B8' : '#64748B', marginLeft: 6 }}>›</Text>
          </View>

          <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]} numberOfLines={1}>
            Prof: {String(teacher)} • Salle: {String(room)}
          </Text>
          {item.notes ? (
            <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]} numberOfLines={2}>
              Notes: {item.notes}
            </Text>
          ) : null}
        </View>

        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            onPress={() => onEdit(String((item as any).id))}
            style={[styles.smallBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            accessibilityLabel="Éditer"
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>Éditer</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => onDelete(String((item as any).id))}
            style={[styles.smallBtn, { borderColor: 'rgba(239,68,68,0.35)', backgroundColor: 'rgba(239,68,68,0.08)' }]}
            accessibilityLabel="Supprimer"
          >
            <Text style={{ fontWeight: '800', color: '#ef4444' }}>Supprimer</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderSectionHeader = ({ section }: { section: Section }) => (
    <View style={{ backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : '#F3F4F6', paddingVertical: 6, paddingHorizontal: 8, borderRadius: 8, marginTop: 8 }}>
      <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#111827', textTransform: 'capitalize' }}>{section.title}</Text>
    </View>
  );

  return (
    <SectionList
      sections={sections}
      keyExtractor={(item) => String((item as any).id)}
      renderItem={renderItem}
      renderSectionHeader={renderSectionHeader}
      contentContainerStyle={sections.length ? { paddingHorizontal: 16, paddingTop: 8, paddingBottom: 24 } : { flexGrow: 1, justifyContent: 'center', paddingHorizontal: 16 }}
      ListEmptyComponent={
        <View style={{ alignItems: 'center' }}>
          <Text style={{ fontSize: 48, marginBottom: 8 }} accessible accessibilityLabel="Aucune entrée">🗓️</Text>
          <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Aucune entrée d’emploi du temps</Text>
        </View>
      }
      stickySectionHeadersEnabled
    />
  );
};

const styles = StyleSheet.create({
  item: { padding: 14, borderRadius: 16, borderWidth: 1, marginBottom: 10, flexDirection: 'row', alignItems: 'center', gap: 10 },
  itemTitle: { fontSize: 16, fontWeight: '800' },
  itemMeta: { fontSize: 12, marginTop: 4 },
  chip: { paddingVertical: 4, paddingHorizontal: 8, borderWidth: 1, borderRadius: 999 },
  smallBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
});

export default memo(TimetableList);