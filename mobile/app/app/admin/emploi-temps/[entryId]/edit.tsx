import React, { useEffect, useState, useLayoutEffect } from 'react';
import { ActivityIndicator, Alert, StyleSheet, Text, View, TouchableOpacity, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import TimetableForm from '../../../../../src/components/admin/emploi-temps/TimetableForm';
import { useTimetableEntry } from '../../../../../src/hooks/useTimetableEntry';
import type { UpdateTimetableEntryInput, TimetableEntry } from '../../../../../src/types/timetable';
import { useNavigation } from '@react-navigation/native';

export default function EditTimetableEntryScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const params = useLocalSearchParams<{ entryId: string; classId?: string }>();
  const entryId = params.entryId ? String(params.entryId) : '';
  const classId = params.classId ? String(params.classId) : undefined;

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  useLayoutEffect(() => {
    navigation.setOptions({
      headerShown: true,
      title: 'Modifier horaire',
      headerBackTitle: 'Retour',
    });
  }, [navigation]);

  const { getEntry, updateEntry, deleteEntry, isMutating, error } = useTimetableEntry(classId);
  const [initial, setInitial] = useState<TimetableEntry | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        if (!classId) {
          throw new Error('Backend does not expose GET /api/emploi_temps/:id; provide classId to resolve via listByClass.');
        }
        const data = await getEntry(entryId);
        if (mounted) setInitial(data || null);
      } catch {
        // surfaced below
      } finally {
        if (mounted) setIsLoading(false);
      }
    })();
    return () => {
      mounted = false;
    };
  }, [entryId, classId, getEntry]);

  const handleSubmit = async (patch: UpdateTimetableEntryInput) => {
    try {
      await updateEntry(entryId, patch);
      const targetClassId = patch.classId ?? initial?.classId ?? classId;
      if (targetClassId) {
        router.replace(`/app/admin/emploi-temps/${targetClassId}`);
      } else {
        router.back();
      }
    } catch {
      // error shown via banner
    }
  };

  const handleDelete = () => {
    Alert.alert('Confirmer la suppression', 'Voulez-vous supprimer cette entrée ?', [
      { text: 'Annuler', style: 'cancel' },
      {
        text: 'Supprimer',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteEntry(entryId);
            const targetClassId = initial?.classId ?? classId;
            if (targetClassId) {
              router.replace(`/app/admin/emploi-temps/${targetClassId}`);
            } else {
              router.back();
            }
          } catch {
            // surfaced below
          }
        },
      },
    ]);
  };

  if (isLoading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement de l’entrée...</Text>
      </View>
    );
  }

  if (!classId) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444', textAlign: 'center' }}>
          Impossible de charger l’entrée.&nbsp;
          Le backend ne fournit pas GET /api/emploi_temps/:id; le classId est requis pour résoudre via listByClass.
        </Text>
        <View style={{ marginTop: 8 }}>
          <TouchableOpacity onPress={() => router.back()} style={[styles.secondaryBtn]}>
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>Revenir</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (!initial) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444' }}>Entrée introuvable.</Text>
        <View style={{ marginTop: 8 }}>
          <TouchableOpacity onPress={() => router.back()} style={[styles.secondaryBtn]}>
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>Retour</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const ErrorBanner = error ? (
    <View style={[styles.alert, { borderColor: '#ef4444', backgroundColor: 'rgba(239,68,68,0.08)' }]}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
        <Text style={{ color: '#ef4444', flex: 1 }} numberOfLines={2}>{String(error)}</Text>
        <TouchableOpacity onPress={() => { /* no refetch here; edits are local */ }} style={[styles.retryBtn, { backgroundColor: '#ef4444' }]} accessibilityLabel="Fermer">
          <Text style={{ color: '#fff', fontWeight: '800' }}>OK</Text>
        </TouchableOpacity>
      </View>
    </View>
  ) : null;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Stack.Screen options={{ title: 'Modifier horaire', headerBackTitle: 'Retour' }} />
      {ErrorBanner}
      <View style={{ flex: 1 }}>
        <View style={{ paddingHorizontal: 16, paddingTop: 16 }}>
          <View style={[styles.headerWrap, { marginBottom: 12, borderBottomWidth: 1, borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)' }]}>
            <View style={[styles.headerRow, { justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }]}>
              <TouchableOpacity
                onPress={() => router.back()}
                accessibilityRole="button"
                accessibilityLabel="Retour"
                style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
              >
                <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
              </TouchableOpacity>
              {isMutating ? (
                <View style={{ paddingVertical: 8, paddingHorizontal: 12 }}>
                  <ActivityIndicator />
                </View>
              ) : (
                <TouchableOpacity onPress={handleDelete} style={styles.dangerBtn} accessibilityLabel="Supprimer">
                  <Text style={{ color: '#fff', fontWeight: '800' }}>Supprimer</Text>
                </TouchableOpacity>
              )}
            </View>
            <View style={{ paddingBottom: 8 }}>
              <Text
                style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                Modifier l’entrée
              </Text>
            </View>
          </View>
        </View>

        {isMutating ? (
          <View style={[styles.center, { paddingHorizontal: 16 }]}>
            <ActivityIndicator />
          </View>
        ) : null}

        <View style={{ flex: 1, paddingHorizontal: 16, paddingBottom: 16 }}>
          {/* Make form scrollable to avoid overflow issues on smaller screens/keyboard open */}
          <View style={{ flex: 1 }}>
            <SafeAreaView edges={['bottom']} style={{ flex: 1 }}>
              <View style={{ flex: 1 }}>
                <View style={{ flex: 1, overflow: 'hidden' }}>
                  <View style={{ flex: 1 }}>
                    <View style={{ flexGrow: 1 }}>
                      <TimetableForm
                        classIdLocked={true}
                        initial={initial}
                        onSubmit={handleSubmit}
                        onCancel={() => router.back()}
                        submitLabel="Mettre à jour"
                      />
                    </View>
                  </View>
                </View>
              </View>
            </SafeAreaView>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerWrap: {},
  headerRow: { flexDirection: 'row' },
  title: { fontSize: 20, fontWeight: '800' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 10, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 72, alignItems: 'center' },
  secondaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },
  dangerBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#ef4444' },
  alert: { padding: 10, borderRadius: 8, borderWidth: 1, margin: 16, marginBottom: 0 },
  retryBtn: { backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
});
