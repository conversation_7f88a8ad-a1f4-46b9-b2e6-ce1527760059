/**
 * SUMMARY: Uses Select-based TimetableForm with dropdowns for Classe/Cours/Prof/Salle.
 * - Pre-locks class selection when classId param is provided (value is nom_classe).
 * - Shows inline error banner, disables submit when mutating, and navigates back to class screen on success.
 */
import React, { useLayoutEffect } from 'react';
import { ActivityIndicator, StyleSheet, Text, View, TouchableOpacity, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import TimetableForm from '../../../../src/components/admin/emploi-temps/TimetableForm';
import { useTimetableEntry } from '../../../../src/hooks/useTimetableEntry';
import type { CreateTimetableEntryInput } from '../../../../src/types/timetable';
import type { UpdateTimetableEntryInput } from '../../../../src/types/timetable';
import { useNavigation } from '@react-navigation/native';

export default function CreateTimetableEntryScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const params = useLocalSearchParams<{ classId?: string }>();
  const classId = params.classId ? String(params.classId) : undefined;

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  useLayoutEffect(() => {
    navigation.setOptions({
      headerShown: true,
      title: 'Nouvel horaire',
      headerBackTitle: 'Retour',
    });
  }, [navigation]);

  const { createEntry, isMutating, error } = useTimetableEntry(classId);

  const handleSubmit = async (input: CreateTimetableEntryInput | UpdateTimetableEntryInput) => {
    try {
      await createEntry(input as CreateTimetableEntryInput);
      const targetClassId = input.classId ?? classId;
      if (targetClassId) {
        router.replace({ pathname: '/app/admin/emploi-temps/[classId]', params: { classId: String(targetClassId) } as any });
      } else {
        router.back();
      }
    } catch {
      // surfaced via error
    }
  };

  const ErrorBanner = error ? (
    <View style={[styles.alert, { borderColor: '#ef4444', backgroundColor: 'rgba(239,68,68,0.08)' }]}>
      <Text style={{ color: '#ef4444' }}>Erreur: {String(error)}</Text>
    </View>
  ) : null;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Stack.Screen options={{ title: 'Nouvel horaire', headerBackTitle: 'Retour' }} />
      {ErrorBanner}
      <View style={{ padding: 16 }}>
        <View style={[styles.headerRow, { justifyContent: 'space-between', marginBottom: 12 }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Retour"
            style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Nouvelle entrée</Text>
          <View style={{ width: 80 }} />
        </View>

        {isMutating ? (
          <View style={styles.center}>
            <ActivityIndicator />
          </View>
        ) : null}

        <TimetableForm
          classIdLocked={!!classId}
          initial={classId ? { classId } : undefined}
          onSubmit={handleSubmit}
          onCancel={() => router.back()}
          submitLabel="Créer"
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center' },
  title: { fontSize: 20, fontWeight: '800' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  alert: { padding: 10, borderRadius: 8, borderWidth: 1, margin: 16, marginBottom: 0 },
  center: { alignItems: 'center', justifyContent: 'center' },
});
