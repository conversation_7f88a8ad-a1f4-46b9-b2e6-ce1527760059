import React, { useMemo, useState, useCallback, useEffect, memo } from 'react';
import { ActivityIndicator, Alert, StyleSheet, Text, TextInput, View, TouchableOpacity, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useClasses } from '../../../../src/hooks/useClasses';

export default function AdminEmploiTempsIndex() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { data, loading, error, refetch } = useClasses();
  const [selectedClassId, setSelectedClassId] = useState<string>('');

  // Replicate the "back" UX pattern from Courses: inline back button in a header row

  const items = useMemo(() => {
    // Normalize to avoid undefined keys/labels
    return (data ?? []).map((c: any, idx: number) => {
      // Try common id fields
      const rawId = c?.id ?? c?.nom_classe ?? c?.nom ?? c?.name ?? idx;
      const id = String(rawId);
      // Prefer human label fields in order
      const label =
        c?.nom ??
        c?.name ??
        c?.nom_classe ??
        (typeof c?.id !== 'undefined' ? `Classe ${c.id}` : `Classe ${idx + 1}`);
      return { id, name: label };
    });
  }, [data]);

  const handleNavigate = useCallback(() => {
    if (!selectedClassId.trim()) {
      Alert.alert('Sélection requise', 'Veuillez sélectionner une classe.');
      return;
    }
    router.push({ pathname: '/app/admin/emploi-temps/[classId]', params: { classId: selectedClassId.trim() } as any });
  }, [router, selectedClassId]);

  // Loading placeholder consistent with Courses
  if (loading && (!data || data.length === 0)) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement des classes...</Text>
      </View>
    );
  }

  const showErrorBanner = !!error;
  const ErrorBanner = showErrorBanner ? (
    <View style={[styles.alert, { borderColor: '#ef4444', backgroundColor: 'rgba(239,68,68,0.08)' }]}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
        <Text style={{ color: '#ef4444', flex: 1 }} numberOfLines={2}>{String(error)}</Text>
        <TouchableOpacity onPress={() => refetch?.()} style={[styles.retryBtn, { backgroundColor: '#ef4444' }]} accessibilityLabel="Réessayer">
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    </View>
  ) : null;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      {ErrorBanner}
      <View style={{ padding: 16 }}>
        <View style={[styles.headerRow, { justifyContent: 'space-between', marginBottom: 12 }]}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <TouchableOpacity
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
              style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            >
              <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
            </TouchableOpacity>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Emploi du temps</Text>
          </View>
          <TouchableOpacity
            onPress={handleNavigate}
            style={[styles.createBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            accessibilityRole="button"
            accessibilityLabel="Voir l’emploi du temps"
          >
            <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Ouvrir</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.field}>
          <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Sélectionner une classe</Text>
          <TextInput
            placeholder="Entrer l'identifiant de la classe"
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            value={selectedClassId}
            onChangeText={setSelectedClassId}
            style={[
              styles.input,
              {
                color: isDark ? '#F8FAFC' : '#0F172A',
                borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
                backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
              },
            ]}
          />
        </View>

        <TouchableOpacity
          onPress={handleNavigate}
          style={[styles.submitBtn, { backgroundColor: selectedClassId.trim() ? '#2563EB' : '#93C5FD' }]}
          disabled={!selectedClassId.trim()}
          accessibilityRole="button"
          accessibilityLabel="Voir l’emploi du temps"
        >
          <Text style={{ color: '#fff', fontWeight: '800' }}>Voir l’emploi du temps</Text>
        </TouchableOpacity>

        <View style={{ marginTop: 16 }}>
          {items.length > 0 ? (
            <View>
              <Text style={[styles.label, { marginBottom: 6, color: isDark ? '#CBD5E1' : '#334155' }]}>
                Ou choisir ci-dessous (remplit l'identifiant):
              </Text>
              {items.map((it, idx) => (
                <TouchableOpacity
                  key={`${it.id}-${idx}`}
                  onPress={() => setSelectedClassId(it.id)}
                  style={[
                    styles.optionRow,
                    {
                      backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
                      borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
                    },
                  ]}
                  activeOpacity={0.7}
                  accessibilityLabel={`Classe ${it.name}`}
                >
                  <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A' }}>
                    {it.name} — id: {it.id}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Aucune classe disponible.</Text>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center' },
  title: { fontSize: 20, fontWeight: '800' },
  field: { marginBottom: 12 },
  label: { fontWeight: '800', marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, padding: 10 },
  optionRow: { paddingVertical: 10, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, marginBottom: 8 },
  submitBtn: { marginTop: 8, paddingVertical: 12, alignItems: 'center', borderRadius: 12 },
  retryBtn: { backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  alert: { padding: 10, borderRadius: 8, borderWidth: 1, margin: 16, marginBottom: 0 },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
  // Buttons styled like Courses screen:
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  createBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
});
