import React, { useMemo, useCallback } from 'react';
import { ActivityIndicator, Alert, StyleSheet, Text, View, TouchableOpacity, useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useTimetables } from '../../../../../src/hooks/useTimetables';
import { useTimetableEntry } from '../../../../../src/hooks/useTimetableEntry';
import TimetableList from '../../../../../src/components/admin/emploi-temps/TimetableList';

export default function ClassTimetableScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ classId: string }>();
  const classId = params.classId ? String(params.classId) : '';

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Header will include an inline back button similar to Courses screen

  const { data, groupedByDay, isLoading, error, refetch } = useTimetables(classId);
  const { deleteEntry } = useTimetableEntry(classId);

  const flatEntries = useMemo(() => {
    if (data) return data;
    if (!groupedByDay) return [];
    return Object.values(groupedByDay).flat();
  }, [data, groupedByDay]);

  const handleAdd = useCallback(() => {
    router.push({ pathname: '/app/admin/emploi-temps/create', params: { classId } as any });
  }, [router, classId]);

  const handleEdit = useCallback((entryId: string) => {
    router.push({ pathname: '/app/admin/emploi-temps/[entryId]/edit', params: { entryId, classId } as any });
  }, [router, classId]);

  const handleDelete = useCallback((entryId: string) => {
    Alert.alert('Confirmer la suppression', 'Voulez-vous supprimer cette entrée ?', [
      { text: 'Annuler', style: 'cancel' },
      {
        text: 'Supprimer',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteEntry(entryId);
            await refetch();
          } catch {
            // surfaced below
          }
        },
      },
    ]);
  }, [deleteEntry, refetch]);

  // Loading aligned with Courses
  if (isLoading && (!flatEntries || flatEntries.length === 0)) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement de l’emploi du temps...</Text>
      </View>
    );
  }

  const showErrorBanner = !!error;
  const ErrorBanner = showErrorBanner ? (
    <View style={[styles.alert, { borderColor: '#ef4444', backgroundColor: 'rgba(239,68,68,0.08)' }]}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
        <Text style={{ color: '#ef4444', flex: 1 }} numberOfLines={2}>{String(error)}</Text>
        <TouchableOpacity onPress={() => refetch()} style={[styles.retryBtn, { backgroundColor: '#ef4444' }]} accessibilityLabel="Réessayer">
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    </View>
  ) : null;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Stack.Screen options={{ title: 'Emploi du temps — Classe', headerBackTitle: 'Retour' }} />
      {ErrorBanner}
      <View style={{ padding: 16 }}>
        <View style={[styles.headerWrap, { marginBottom: 12, borderBottomWidth: 1, borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)' }]}>
          <View style={[styles.headerRow, { justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }]}>
            <TouchableOpacity
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
              style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            >
              <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleAdd}
              style={[styles.primaryBtn]}
              accessibilityLabel="Ajouter"
            >
              <Text style={{ fontWeight: '800', color: '#FFFFFF' }}>Ajouter</Text>
            </TouchableOpacity>
          </View>
          <View style={{ paddingBottom: 8 }}>
            <Text
              style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              Emploi du temps — Classe {classId}
            </Text>
          </View>
        </View>

        <TimetableList
          entries={flatEntries}
          onEdit={(id) => handleEdit(String(id))}
          onDelete={(id) => handleDelete(String(id))}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerWrap: { },
  headerRow: { flexDirection: 'row' },
  title: { fontSize: 20, fontWeight: '800' },
  alert: { padding: 10, borderRadius: 8, borderWidth: 1, margin: 16, marginBottom: 0 },
  retryBtn: { backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
  // Back button compact to reduce overflow
  backBtn: { paddingVertical: 8, paddingHorizontal: 10, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 72, alignItems: 'center' },
  // Primary action stays prominent on header right
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },
});
