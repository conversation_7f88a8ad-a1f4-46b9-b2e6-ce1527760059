# Mobile Admin — Emploi du temps (Timetable) Refactor Plan

Scope
- Replace raw ID text inputs with dropdown Selects for <PERSON><PERSON>, Co<PERSON>, Professeur, <PERSON> across add/edit flows.
- Keep backend unchanged. Adapt client to existing API shapes and current services.

Key Files
- Screens:
  - mobile/app/app/admin/emploi-temps/index.tsx
  - mobile/app/app/admin/emploi-temps/[classId]/index.tsx
  - mobile/app/app/admin/emploi-temps/create.tsx
  - mobile/app/app/admin/emploi-temps/[entryId]/edit.tsx
- Form:
  - mobile/src/components/admin/emploi-temps/TimetableForm.tsx
- Services:
  - mobile/src/services/emploiTemps.ts
  - mobile/src/services/classes.ts
  - mobile/src/services/courses.ts
  - mobile/src/services/sallesService.ts
  - mobile/src/services/apiService.ts (ApiServices.user().getProfesseurs)
- Types:
  - mobile/src/types/timetable.ts
- Docs:
  - docs/api/emploi-temps.md

Audit Summary
- TimetableForm currently uses TextInput for classId, courseId, teacherId, roomId, and manual day/time fields.
- emploiTemps service uses create payload with nom_classe mapped from input.classId, and update payload with *_id fields.
- List services exist:
  - Classes: classesService.list() -> rows include nom_classe
  - Courses: coursesService.list() -> id/nom
  - Salles: sallesService.getSalles() -> map salle_id or id
  - Profs: ApiServices.user().getProfesseurs() -> matricule, nom, prenom
- Day list and time validation utilities already exist.

What to Implement

1) Replace Raw ID Fields with Selects
- In TimetableForm:
  - Classe Select:
    - Source: classesService.list()
    - Label/value: label=nom_classe, value=nom_classe
    - When classIdLocked=true, disable the Select.
    - Use classId to carry the selected nom_classe (per current backend expectation for create).
  - Cours Select:
    - Source: coursesService.list()
    - Label: nom (fallback: matiere/name), Value: id (fallback: matiere_id/cours_id)
  - Professeur Select:
    - Source: ApiServices.user().getProfesseurs()
    - Label: "{nom} {prenom} ({matricule})"
    - Value: matricule
  - Salle Select:
    - Source: sallesService.getSalles() (array or { data: [] })
    - Label: nom/name/code, Value: id (fallback: salle_id/code)

2) Day and Time Inputs
- Keep existing day chips (Monday–Saturday; Sunday allowed in types; restrict to Monday–Saturday if required by business).
- Keep startTime/endTime TextInput fields with HH:mm format validation.
- Validate endTime must be after startTime.

3) Form State and Validation
- Maintain selected entity values in local state:
  - classId: string (nom_classe)
  - courseId: string (course id)
  - teacherId: string (prof matricule)
  - roomId: string (salle id)
- Validation Rules:
  - Required: classId (unless locked), day, startTime, endTime, courseId, teacherId, roomId
  - Time must be in HH:mm; endTime strictly after startTime
- Inline error messages next to fields; keep form banner for submit errors.

4) Data Mapping to Services
- Create (emploiTemps.create):
  - Payload mapping is handled by service:
    - nom_classe ⇐ input.classId (note: value is the class name)
    - matiere_id ⇐ input.courseId
    - salle_id ⇐ input.roomId
    - jour_semaine ⇐ input.day
    - heure_debut ⇐ input.startTime
    - heure_fin ⇐ input.endTime
- Update (emploiTemps.update):
  - Service maps patch fields to snake_case keys:
    - classe_id ⇐ patch.classId
    - jour/day ⇐ patch.day
    - heure_debut ⇐ patch.startTime
    - heure_fin ⇐ patch.endTime
    - cours_id ⇐ patch.courseId
    - prof_id ⇐ patch.teacherId
    - salle_id ⇐ patch.roomId
- Edit screen should pre-populate Selects from initial entry; if only IDs, labels are resolved by subsequent list fetches.

5) UX/Loading/Error Handling
- Fetch options in TimetableForm with loading state/spinner.
- Handle and display errors if fetching options fails; keep form usable where possible.
- Disable submit button while options are loading or when form is invalid.
- On success, navigate back to timetable list for selected class; on error, display inline/banner error and stay on form.

6) Non-functional/Component Reuse
- Reuse existing Select component:
  - Import: mobile/src/components/Select.tsx
  - Use as a modal dropdown with label, placeholder, and options.
- Avoid adding new dependencies.
- Maintain code style, theming, and accessibility labels consistent with existing screens.
- Extract nothing new unless necessary; TimetableForm is the integration point.

7) Types
- Keep Timetable types:
  - TimetableEntry, CreateTimetableEntryInput, UpdateTimetableEntryInput already suitable.
- If needed, extend TimetableEntry with optional display fields (courseName, teacherName, roomName), already present in service mapping.

8) Screens Wiring
- create.tsx:
  - Pass classId via route param to lock class selection if navigated from a class context.
  - Show error banner, spinner while mutating.
  - On success: router.replace('/app/admin/emploi-temps/[classId]').
- [entryId]/edit.tsx:
  - Fetch initial via useTimetableEntry + classId list-by-class resolution.
  - Lock class selection in form.
  - Keep delete flow unchanged.

9) Edge Cases
- Large lists (50+ items): the current Select shows a scrollable modal. If server supports query params later, debounce search can be added to services and form in a follow-up.
- Inconsistent backend shapes: services already normalize responses.
- Missing labels:
  - Courses: fallback label "Cours {id}"
  - Salles: fallback label "Salle {id}"
  - Profs: fallback "Prof {matricule}"

Change Log Requirements
- Add a summary comment at the top of each modified file describing:
  - Replaced text inputs with Select components for Classe/Cours/Prof/Salle.
  - Loading of options from services; validation and disabled submit state.
  - Mapping details from selected values to service payload fields.

Completion Criteria
- TimetableForm renders Selects and validates per rules.
- Create/Edit flows work with selects; navigation succeeds after successful mutations.
- No backend changes were made.
- No regressions to listing screens.

